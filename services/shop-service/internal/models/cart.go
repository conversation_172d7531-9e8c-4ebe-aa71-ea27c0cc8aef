package models

import (
	"time"

	"go.mongodb.org/mongo-driver/v2/bson"
)

type CartItem struct {
	ProductID bson.ObjectID `json:"product_id" bson:"product_id"`
	Product   *Product      `json:"product,omitempty" bson:"product,omitempty"`
	Quantity  int           `json:"quantity" bson:"quantity" binding:"required,gt=0"`
	Price     float64       `json:"price" bson:"price"`
	Total     float64       `json:"total" bson:"total"`
}

type Cart struct {
	ID        bson.ObjectID `json:"id" bson:"_id,omitempty"`
	UserID    uint          `json:"user_id" bson:"user_id"`
	Items     []CartItem    `json:"items" bson:"items"`
	Total     float64       `json:"total" bson:"total"`
	CreatedAt time.Time     `json:"created_at" bson:"created_at"`
	UpdatedAt time.Time     `json:"updated_at" bson:"updated_at"`
}

type AddToCartRequest struct {
	ProductID string `json:"product_id" binding:"required"`
	Quantity  int    `json:"quantity" binding:"required,gt=0"`
}

type UpdateCartItemRequest struct {
	Quantity int `json:"quantity" binding:"required,gt=0"`
}

type CartResponse struct {
	ID        string     `json:"id"`
	UserID    uint       `json:"user_id"`
	Items     []CartItem `json:"items"`
	Total     float64    `json:"total"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
}
