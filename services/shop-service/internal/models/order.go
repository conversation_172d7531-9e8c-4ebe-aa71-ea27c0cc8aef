package models

import (
	"time"

	"go.mongodb.org/mongo-driver/v2/bson"
)

type OrderStatus string

const (
	OrderStatusPending   OrderStatus = "pending"
	OrderStatusConfirmed OrderStatus = "confirmed"
	OrderStatusShipped   OrderStatus = "shipped"
	OrderStatusDelivered OrderStatus = "delivered"
	OrderStatusCancelled OrderStatus = "cancelled"
)

type OrderItem struct {
	ProductID   bson.ObjectID `json:"product_id" bson:"product_id"`
	ProductName string        `json:"product_name" bson:"product_name"`
	Quantity    int           `json:"quantity" bson:"quantity"`
	Price       float64       `json:"price" bson:"price"`
	Total       float64       `json:"total" bson:"total"`
}

type ShippingAddress struct {
	Street  string `json:"street" bson:"street" binding:"required"`
	City    string `json:"city" bson:"city" binding:"required"`
	State   string `json:"state" bson:"state" binding:"required"`
	ZipCode string `json:"zip_code" bson:"zip_code" binding:"required"`
	Country string `json:"country" bson:"country" binding:"required"`
}

type Order struct {
	ID              bson.ObjectID   `json:"id" bson:"_id,omitempty"`
	UserID          uint            `json:"user_id" bson:"user_id"`
	Items           []OrderItem     `json:"items" bson:"items"`
	Total           float64         `json:"total" bson:"total"`
	Status          OrderStatus     `json:"status" bson:"status"`
	ShippingAddress ShippingAddress `json:"shipping_address" bson:"shipping_address"`
	PaymentMethod   string          `json:"payment_method" bson:"payment_method"`
	CreatedAt       time.Time       `json:"created_at" bson:"created_at"`
	UpdatedAt       time.Time       `json:"updated_at" bson:"updated_at"`
}

type CheckoutRequest struct {
	ShippingAddress ShippingAddress `json:"shipping_address" binding:"required"`
	PaymentMethod   string          `json:"payment_method" binding:"required"`
}

type OrderResponse struct {
	ID              string          `json:"id"`
	UserID          uint            `json:"user_id"`
	Items           []OrderItem     `json:"items"`
	Total           float64         `json:"total"`
	Status          OrderStatus     `json:"status"`
	ShippingAddress ShippingAddress `json:"shipping_address"`
	PaymentMethod   string          `json:"payment_method"`
	CreatedAt       time.Time       `json:"created_at"`
	UpdatedAt       time.Time       `json:"updated_at"`
}

type OrderListResponse struct {
	Orders []OrderResponse `json:"orders"`
	Total  int64           `json:"total"`
	Page   int             `json:"page"`
	Limit  int             `json:"limit"`
}
