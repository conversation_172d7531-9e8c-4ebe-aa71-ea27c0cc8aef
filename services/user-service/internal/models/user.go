package models

import (
	"time"

	"gorm.io/gorm"
)

type UserProfile struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	UserID      uint           `json:"user_id" gorm:"uniqueIndex;not null"`
	FirstName   string         `json:"first_name" gorm:"size:100"`
	LastName    string         `json:"last_name" gorm:"size:100"`
	Phone       string         `json:"phone" gorm:"size:20"`
	DateOfBirth time.Time      `json:"date_of_birth" gorm:"type:date"`
	Gender      string         `json:"gender" gorm:"size:10"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

type UserAddress struct {
	ID         uint           `json:"id" gorm:"primaryKey"`
	UserID     uint           `json:"user_id" gorm:"not null;index"`
	Type       string         `json:"type" gorm:"size:20;not null"` // shipping, billing
	Street     string         `json:"street" gorm:"size:255;not null"`
	City       string         `json:"city" gorm:"size:100;not null"`
	State      string         `json:"state" gorm:"size:100;not null"`
	PostalCode string         `json:"postal_code" gorm:"size:20;not null"`
	Country    string         `json:"country" gorm:"size:100;not null"`
	IsDefault  bool           `json:"is_default" gorm:"default:false"`
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"index"`
}

type UserPreferences struct {
	ID                   uint           `json:"id" gorm:"primaryKey"`
	UserID               uint           `json:"user_id" gorm:"uniqueIndex;not null"`
	NewsletterSubscribed bool           `json:"newsletter_subscribed" gorm:"default:false"`
	PreferredCurrency    string         `json:"preferred_currency" gorm:"size:3;default:'USD'"`
	PreferredLanguage    string         `json:"preferred_language" gorm:"size:5;default:'en'"`
	CreatedAt            time.Time      `json:"created_at"`
	UpdatedAt            time.Time      `json:"updated_at"`
	DeletedAt            gorm.DeletedAt `json:"-" gorm:"index"`
}

// Request/Response models
type UpdateProfileRequest struct {
	FirstName   *string `json:"first_name,omitempty"`
	LastName    *string `json:"last_name,omitempty"`
	Phone       *string `json:"phone,omitempty"`
	DateOfBirth *string `json:"date_of_birth,omitempty"`
	Gender      *string `json:"gender,omitempty"`
}

type CreateAddressRequest struct {
	Type       string `json:"type" binding:"required,oneof=shipping billing"`
	Street     string `json:"street" binding:"required"`
	City       string `json:"city" binding:"required"`
	State      string `json:"state" binding:"required"`
	PostalCode string `json:"postal_code" binding:"required"`
	Country    string `json:"country" binding:"required"`
	IsDefault  bool   `json:"is_default"`
}

type UpdateAddressRequest struct {
	Type       *string `json:"type,omitempty"`
	Street     *string `json:"street,omitempty"`
	City       *string `json:"city,omitempty"`
	State      *string `json:"state,omitempty"`
	PostalCode *string `json:"postal_code,omitempty"`
	Country    *string `json:"country,omitempty"`
	IsDefault  *bool   `json:"is_default,omitempty"`
}

type UpdatePreferencesRequest struct {
	NewsletterSubscribed *bool   `json:"newsletter_subscribed,omitempty"`
	PreferredCurrency    *string `json:"preferred_currency,omitempty"`
	PreferredLanguage    *string `json:"preferred_language,omitempty"`
}

type ErrorResponse struct {
	Error   string `json:"error"`
	Message string `json:"message"`
}
