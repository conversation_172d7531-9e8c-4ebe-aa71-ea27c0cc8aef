package service

import (
	"errors"
	"time"
	"user-service/internal/kafka"
	"user-service/internal/models"
	"user-service/internal/repository"

	"gorm.io/gorm"
)

type UserService interface {
	GetProfile(userID uint) (*models.UserProfile, error)
	UpdateProfile(userID uint, req *models.UpdateProfileRequest) (*models.UserProfile, error)
	GetAddresses(userID uint) ([]models.UserAddress, error)
	CreateAddress(userID uint, req *models.CreateAddressRequest) (*models.UserAddress, error)
	UpdateAddress(userID uint, addressID uint, req *models.UpdateAddressRequest) (*models.UserAddress, error)
	DeleteAddress(userID uint, addressID uint) error
	GetPreferences(userID uint) (*models.UserPreferences, error)
	UpdatePreferences(userID uint, req *models.UpdatePreferencesRequest) (*models.UserPreferences, error)
}

type userService struct {
	userRepo       repository.UserRepository
	kafkaProducer  *kafka.Producer
	authServiceURL string
}

func NewUserService(userRepo repository.UserRepository, kafkaProducer *kafka.Producer, authServiceURL string) UserService {
	return &userService{
		userRepo:       userRepo,
		kafkaProducer:  kafkaProducer,
		authServiceURL: authServiceURL,
	}
}

func (s *userService) GetProfile(userID uint) (*models.UserProfile, error) {
	profile, err := s.userRepo.GetProfile(userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// Create default profile if not exists
			profile = &models.UserProfile{
				UserID: userID,
			}
			if err := s.userRepo.CreateProfile(profile); err != nil {
				return nil, err
			}
		} else {
			return nil, err
		}
	}
	return profile, nil
}

func (s *userService) UpdateProfile(userID uint, req *models.UpdateProfileRequest) (*models.UserProfile, error) {
	profile, err := s.GetProfile(userID)
	if err != nil {
		return nil, err
	}

	// Store old values for audit
	oldProfile := *profile

	// Update fields if provided
	if req.FirstName != nil {
		profile.FirstName = *req.FirstName
	}
	if req.LastName != nil {
		profile.LastName = *req.LastName
	}
	if req.Phone != nil {
		profile.Phone = *req.Phone
	}
	if req.DateOfBirth != nil {
		dateOfBirth, err := time.Parse("2006-01-02", *req.DateOfBirth)
		if err != nil {
			return nil, err
		}
		profile.DateOfBirth = dateOfBirth
	}
	if req.Gender != nil {
		profile.Gender = *req.Gender
	}

	if err := s.userRepo.UpdateProfile(profile); err != nil {
		return nil, err
	}

	// Publish event to Kafka
	s.kafkaProducer.PublishEvent("user.events", map[string]interface{}{
		"event_type":  "profile_updated",
		"user_id":     userID,
		"timestamp":   time.Now().UTC(),
		"old_profile": oldProfile,
		"new_profile": profile,
		"changes":     req,
	})

	return profile, nil
}

func (s *userService) GetAddresses(userID uint) ([]models.UserAddress, error) {
	return s.userRepo.GetAddresses(userID)
}

func (s *userService) CreateAddress(userID uint, req *models.CreateAddressRequest) (*models.UserAddress, error) {
	address := &models.UserAddress{
		UserID:     userID,
		Type:       req.Type,
		Street:     req.Street,
		City:       req.City,
		State:      req.State,
		PostalCode: req.PostalCode,
		Country:    req.Country,
		IsDefault:  req.IsDefault,
	}

	if err := s.userRepo.CreateAddress(address); err != nil {
		return nil, err
	}

	// Publish event to Kafka
	s.kafkaProducer.PublishEvent("user.events", map[string]interface{}{
		"event_type": "address_created",
		"user_id":    userID,
		"timestamp":  time.Now(),
		"address":    address,
	})

	return address, nil
}

func (s *userService) UpdateAddress(userID uint, addressID uint, req *models.UpdateAddressRequest) (*models.UserAddress, error) {
	address, err := s.userRepo.GetAddressByID(addressID, userID)
	if err != nil {
		return nil, err
	}

	// Store old values for audit
	oldAddress := *address

	// Update fields if provided
	if req.Type != nil {
		address.Type = *req.Type
	}
	if req.Street != nil {
		address.Street = *req.Street
	}
	if req.City != nil {
		address.City = *req.City
	}
	if req.State != nil {
		address.State = *req.State
	}
	if req.PostalCode != nil {
		address.PostalCode = *req.PostalCode
	}
	if req.Country != nil {
		address.Country = *req.Country
	}
	if req.IsDefault != nil {
		address.IsDefault = *req.IsDefault
	}

	if err := s.userRepo.UpdateAddress(address); err != nil {
		return nil, err
	}

	// Publish event to Kafka
	s.kafkaProducer.PublishEvent("user.events", map[string]interface{}{
		"event_type":  "address_updated",
		"user_id":     userID,
		"timestamp":   time.Now().UTC(),
		"address_id":  addressID,
		"old_address": oldAddress,
		"new_address": address,
		"changes":     req,
	})

	return address, nil
}

func (s *userService) DeleteAddress(userID uint, addressID uint) error {
	// Get address first for audit
	address, err := s.userRepo.GetAddressByID(addressID, userID)
	if err != nil {
		return err
	}

	if err := s.userRepo.DeleteAddress(addressID, userID); err != nil {
		return err
	}

	// Publish event to Kafka
	s.kafkaProducer.PublishEvent("user.events", map[string]interface{}{
		"event_type": "address_deleted",
		"user_id":    userID,
		"timestamp":  time.Now(),
		"address_id": addressID,
		"address":    address,
	})

	return nil
}

func (s *userService) GetPreferences(userID uint) (*models.UserPreferences, error) {
	preferences, err := s.userRepo.GetPreferences(userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// Create default preferences if not exists
			preferences = &models.UserPreferences{
				UserID:               userID,
				NewsletterSubscribed: false,
				PreferredCurrency:    "USD",
				PreferredLanguage:    "en",
			}
			if err := s.userRepo.CreatePreferences(preferences); err != nil {
				return nil, err
			}
		} else {
			return nil, err
		}
	}
	return preferences, nil
}

func (s *userService) UpdatePreferences(userID uint, req *models.UpdatePreferencesRequest) (*models.UserPreferences, error) {
	preferences, err := s.GetPreferences(userID)
	if err != nil {
		return nil, err
	}

	// Store old values for audit
	oldPreferences := *preferences

	// Update fields if provided
	if req.NewsletterSubscribed != nil {
		preferences.NewsletterSubscribed = *req.NewsletterSubscribed
	}
	if req.PreferredCurrency != nil {
		preferences.PreferredCurrency = *req.PreferredCurrency
	}
	if req.PreferredLanguage != nil {
		preferences.PreferredLanguage = *req.PreferredLanguage
	}

	if err := s.userRepo.UpdatePreferences(preferences); err != nil {
		return nil, err
	}

	// Publish event to Kafka
	s.kafkaProducer.PublishEvent("user.events", map[string]interface{}{
		"event_type":      "preferences_updated",
		"user_id":         userID,
		"timestamp":       time.Now(),
		"old_preferences": oldPreferences,
		"new_preferences": preferences,
		"changes":         req,
	})

	return preferences, nil
}
