# Build stage
FROM golang:1.23-alpine AS builder

WORKDIR /app

# Install dependencies and debugging tools
RUN apk add --no-cache git delve

# Copy go mod files
COPY go.mod go.sum ./
RUN go mod download

# Copy source code
COPY . .

# Build the application with debug info
RUN CGO_ENABLED=0 GOOS=linux go build -gcflags="all=-N -l" -o main .

# Final stage
FROM alpine:latest

RUN apk --no-cache add ca-certificates tzdata delve
WORKDIR /root/

# Copy the binary from builder stage
COPY --from=builder /app/main .
# Copy source code for debugging
COPY . /app/

# Expose ports - regular port and delve debugger port
EXPOSE 8080 2345

# Run with delve for debugging
CMD ["dlv", "--listen=:2345", "--headless=true", "--api-version=2", "--accept-multiclient", "exec", "/root/main"]
