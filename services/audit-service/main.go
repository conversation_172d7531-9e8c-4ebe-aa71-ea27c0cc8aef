package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"

	"audit-service/internal/config"
	"audit-service/internal/database"
	"audit-service/internal/kafka"
	"audit-service/internal/repository"
	"audit-service/internal/service"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Initialize database
	db, err := database.Connect(cfg.DatabaseURL)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// Initialize repository
	auditRepo := repository.NewAuditRepository(db)

	// Initialize service
	auditService := service.NewAuditService(auditRepo)

	// Initialize Kafka consumer
	// topics := []string{"auth.events", "user.events", "shop.events", "audit.events"}
	topics := []string{"user.events"}
	kafkaConsumer := kafka.NewConsumer(cfg.KafkaBrokers, topics, "audit-service-group", auditService)

	// Setup graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	// Start Kafka consumer
	log.Println("Starting audit service...")
	go func() {
		if err := kafkaConsumer.Start(ctx); err != nil {
			log.Printf("Kafka consumer error: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown
	<-quit
	log.Println("Shutting down audit service...")

	// Cancel context to stop Kafka consumer
	cancel()

	log.Println("Audit service stopped")
}
